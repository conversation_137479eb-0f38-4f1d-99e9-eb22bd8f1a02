<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>USA Regions – Clickable Image Map</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <style>
    body { font-family: system-ui, Arial, sans-serif; margin: 24px; }
    .wrap { max-width: 640px; }
    img { display: block; max-width: none; }
    .legend { margin-top: 12px; font-size: 14px; color: #444; }
    code { background: #f5f5f5; padding: 0 4px; border-radius: 4px; }
  </style>
</head>
<body>
  <div class="wrap">
    <h1>USA Regions (Clickable)</h1>

    <!-- only one image -->
    <img src="USimage.jpg" alt="Color-coded map of the United States" usemap="#usmap">

    <map name="usmap">
      <!-- RECTANGLES -->
      <area shape="rect" coords="30,160,75,184"
            href="#red" alt="West (Red)" data-name="West (Red)">
      <area shape="rect" coords="72,188,173,200"
            href="#blue" alt="Mountain (Blue)" data-name="Mountain (Blue)">
      <area shape="rect" coords="168,216,330,231"
            href="#green" alt="Central (Green)" data-name="Central (Green)">

      <!-- POLYGONS -->
      <area shape="poly" coords="295,53,347,66,413,1,373,257,329,216"
            href="#yellow" alt="East (Yellow)" data-name="East (Yellow)">
      <area shape="poly" coords="9,185,44,180,0,251,39,230,84,239"
            href="#purple" alt="Alaska/Hawaii (Purple)" data-name="Alaska/Hawaii (Purple)">
    </map>

    <p class="legend">
      Map of America
    </p>
  </div>

  <script>
    document.querySelectorAll('area').forEach(a => {
      a.addEventListener('click', e => {
        e.preventDefault();
        alert('You clicked: ' + (a.dataset.name || a.alt));
      });
    });
  </script>
</body>
</html>
